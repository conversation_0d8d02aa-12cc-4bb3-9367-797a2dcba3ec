---
import Footer from '@components/Footer.astro';
import BackLink from '@components/BackLink.astro';

export interface Props {
  pageTitle: string;
  metaDescription?: string;
  showBackLink?: boolean;
  backLinkTitle?: string;
  backLinkUrl?: string;
}

const {
  pageTitle,
  metaDescription,
  showBackLink = false,
  backLinkTitle,
  backLinkUrl
} = Astro.props;

const isDev = import.meta.env.DEV;

// Simple defaults
const siteUrl = 'https://samuelduval.me';
const defaultDescription = '<PERSON>, développeur 3D à Nantes. Tech Lead chez Wonder Partner\'s, spécialisé en développement Unity, réalité augmentée et solutions innovantes.';
const finalDescription = metaDescription || defaultDescription;
const canonicalUrl = new URL(Astro.url.pathname, siteUrl).href;
---

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="generator" content={Astro.generator} />

    <!-- SEO Meta Tags -->
    <title>{pageTitle}</title>
    <meta name="description" content={finalDescription} />
    <meta name="author" content="Samuel Duval" />
    <link rel="canonical" href={canonicalUrl} />

    <!-- Open Graph Meta Tags -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content={pageTitle} />
    <meta property="og:description" content={finalDescription} />
    <meta property="og:url" content={canonicalUrl} />
    <meta property="og:site_name" content="Samuel Duval" />
    <meta property="og:image" content="https://samuelduval.me/og-image.webp" />

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content={pageTitle} />
    <meta name="twitter:description" content={finalDescription} />
    <meta name="twitter:image" content="https://samuelduval.me/og-image.webp" />

    <link rel="preconnect" href="https://rsms.me/" />
    <link
      rel="stylesheet"
      href="https://rsms.me/inter/inter.css"
      media="print"
      onload="this.media='all'"
    />
    <noscript
      ><link rel="stylesheet" href="https://rsms.me/inter/inter.css" type="text/css" /></noscript
    >

    {
      !isDev && (
        <script
          is:inline
          defer
          id="fairlytics-id-ajcu6jd9k7ysd6"
          data-fairlyticskey="a1425ecfc336a87383e79488e075c38b"
          src="https://app.fairlytics.tech/tag/tag.js"
        />
      )
    }
  </head>

  <body>
    <div class="grid-container">
      <aside class="left-column">
        {showBackLink && <BackLink title={backLinkTitle} url={backLinkUrl} />}
      </aside>

      <main class="main-content">
        <slot />
      </main>

      <aside class="right-column">
        <!-- Right column remains empty by default -->
      </aside>
    </div>

    <Footer />
  </body>
</html>

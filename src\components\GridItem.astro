---
import '@styles/global.css';

const { title, description, url, external = false } = Astro.props;
---

<li class="column">
  <div class="title">
    <a href={url} target={external ? '_blank' : '_self'}>{title}</a>
    {
      external && (
        <svg
          viewBox="0 0 24 24"
          width="16"
          height="16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="m19 15.477-.02-9.672a.802.802 0 0 0-.218-.577c-.145-.152-.34-.228-.587-.228H8.499a.751.751 0 0 0-.777.76c0 .199.076.371.227.517.15.145.326.218.525.218h3.733l4.52-.129-1.728 1.54-9.767 9.783a.692.692 0 0 0-.232.518c0 .205.078.387.235.545a.74.74 0 0 0 .542.237.73.73 0 0 0 .527-.224l9.775-9.78 1.544-1.727-.143 4.188v4.065c0 .199.075.376.225.531.15.155.327.232.531.232.202 0 .38-.076.534-.228a.768.768 0 0 0 .23-.569Z"
            fill="#707070"
          />
        </svg>
      )
    }
  </div>
  <p>{description}</p>
</li>

<style>
  li {
    display: flex;
    flex-direction: column;

    min-height: 84px;
    min-height: clamp(48px, 10vw, 84px);

    margin-top: 12px;
  }

  li > p {
    margin: 0;
    color: var(--color-secondary);
  }

  a {
    font-weight: 500;
  }

  .title {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 4px;
  }
</style>

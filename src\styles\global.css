/* Optimized fallback font for Overused Grotesk */
@font-face {
  font-family: "Overused Grotesk Fallback";
  src: local("Arial"), local("Helvetica Neue"), local("Helvetica"), local("sans-serif");
  size-adjust: 95%;
  ascent-override: 90%;
  descent-override: 22%;
  line-gap-override: 0%;
}

@font-face {
  font-family: "Overused Grotesk";
  src: url("/fonts/OverusedGrotesk-VF.woff2") format("woff2-variations");
  font-weight: 300 900;
  font-display: optional;
}

:root {
  color-scheme: light dark;
  --color-primary: light-dark(#3a3a3a, #e5e5e5);
  --color-secondary: light-dark(#6f6f6f, #a0a0a0);
  --color-tertiary: light-dark(#767676, #828282);
  --color-bg: light-dark(#fff, #1a1a1a);
  --color-underline: light-dark(#a0a0a0, #505050);
  --color-border: light-dark(#e8e8e8, #2e2e2e);
  --color-scrollbar: light-dark(#e8e8e8, #2e2e2e);

  --font-primary: "Overused Grotesk", "Overused Grotesk Fallback", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  --font-secondary: "Inter", sans-serif;
  --font-tertiary: serif;
  font-feature-settings: 'liga' 1, 'calt' 1; /* fix for Chrome */

  --page-width: 1072px;
  --page-top: clamp(64px, 10vw, 128px);
  --page-bottom: clamp(64px, 10vw, 128px);

  --body-margins-left-right: 24px;

  --content-width: 640px;
}

@supports (font-variation-settings: normal) {
  :root { font-family: InterVariable, sans-serif; }
}

@media (prefers-color-scheme: dark) {

  .astro-code,
  .astro-code span {
    color: var(--shiki-dark) !important;
    background-color: var(--shiki-dark-bg) !important;
  }
}

*,
*::before,
*::after {
  margin: 0;
  box-sizing: border-box;
  scrollbar-width: thin;
  scrollbar-color: var(--color-scrollbar) transparent;
}

html,
body {
  height: 100%;
}

body {
  font-family: var(--font-primary);

  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  padding: 0px var(--body-margins-left-right);

  line-height: 1.6;

  background-color: var(--color-bg);

  -webkit-font-smoothing: antialiased;
}

.grid-container {
  display: grid;
  grid-template-columns: 1fr var(--content-width) 1fr;
  column-gap: var(--body-margins-left-right);
  width: 100%;
  max-width: var(--page-width);
  flex: 1 0 auto;

  padding-top: var(--page-top);
  padding-bottom: var(--page-bottom);
}

.left-column {
  grid-column: 1;
}

.main-content {
  grid-column: 2;
  width: 100%;
}

.right-column {
  grid-column: 3;
}

/* Mobile responsive: collapse to single column */
@media (max-width: 768px) {
  .grid-container {
    display: block;
    max-width: var(--content-width);
  }

  .left-column,
  .main-content,
  .right-column {
    grid-column: unset;
    padding: 0;
  }

  .left-column {
    padding-bottom: 0;
  }
}

section {
  margin-bottom: 48px;
}

h1,
h2,
h3 {
  font-family:
    var(--font-secondary),
    sans-serif;
  font-size: 1rem;
  font-weight: 500;
  line-height: 28px;

  margin: 0 0 28px;

  color: var(--color-primary);
}

a {
  text-underline-offset: 2.5px;
  text-decoration: underline;
  text-decoration-thickness: 0.5px;
  text-decoration-color: var(--color-underline);

  color: var(--color-primary);
}

a:hover {
  text-decoration-color: var(--color-primary);
}

p {
  text-wrap: pretty;
  color: var(--color-primary);

  margin: 0 0 28px;
}

/* Inline code */
p>code {
  padding: 4px;

  border-radius: 6px;
  background-color: var(--color-border);
}

article>pre {
  padding: 16px;
  margin: 0 0 28px;

  border-radius: 8px;
}

hr {
  height: 1px;
  border: none;
  background: var(--color-border);
  margin-bottom: 28px;
}

ul {
  padding: 0 1rem;
  margin: 1rem 0 28px;
}

li {
  color: var(--color-primary);
}

li::marker {
  color: var(--color-secondary);
}

blockquote {
  padding: 0 1rem;
  margin: 1rem 0 28px;
}

blockquote>p {
  color: var(--color-secondary);
}

blockquote>p::before {
  content: "“";
  padding-right: 0.1rem;
  color: var(--color-secondary);
}

blockquote>p::after {
  content: "”";
  padding-left: 0.1rem;
  color: var(--color-secondary);
}